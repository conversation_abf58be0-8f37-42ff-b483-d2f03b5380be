using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Controllers;
using Axon.Core.Authentication.Services;
using Axon.Core.Authentication.Utilities;
using CommunityToolkit.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Shouldly;
using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Xunit;

namespace Axon.Core.Authentication.Tests.Controllers;

public class AxonLoginControllerTests
{
    // Test constants
    private const string TestDefaultRedirectUrl = "/dashboard";
    private const string TestIssuer = "test-issuer";
    private const string TestAudience = "test-audience";
    private const string TestPublicKeyApiHost = "https://test-api.com";
    private const string TestRedirectUrl = "https://example.com/dashboard";
    private const string TestEmptyRedirectUrl = "https://example.com";
    private const string TestAuthenticationType = "test";
    private const string TestExceptionMessage = "Test exception";

    private readonly ITokenService tokenService;
    private readonly IAuthenticationService authenticationService;
    private readonly IOptionsMonitor<JwtAuthenticationOptions> options;
    private readonly ILogger<AxonLoginController> logger;
    private readonly AxonLoginController controller;
    private readonly JwtAuthenticationOptions jwtOptions;

    public AxonLoginControllerTests()
    {
        tokenService = Substitute.For<ITokenService>();
        authenticationService = Substitute.For<IAuthenticationService>();
        options = Substitute.For<IOptionsMonitor<JwtAuthenticationOptions>>();
        logger = Substitute.For<ILogger<AxonLoginController>>();

        // Initialize jwtOptions with default values
        jwtOptions = new JwtAuthenticationOptions
        {
            DefaultRedirectUrl = TestDefaultRedirectUrl,
            Issuer = TestIssuer,
            Audience = TestAudience,
            PublicKeyApiHost = TestPublicKeyApiHost
        };

        // Configure the options mock to return our jwtOptions
        options.CurrentValue.Returns(jwtOptions);

        controller = new AxonLoginController(tokenService, authenticationService, options, logger)
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            }
        };
    }

    // Helper methods
    private static string GenerateTestCode() => Fake.GetRandomString(10);
    private static string GenerateTestToken() => Fake.GetRandomString(10);

    private static ClaimsPrincipal CreateAuthenticatedClaimsPrincipal() =>
        new(new ClaimsIdentity(authenticationType: TestAuthenticationType));

    private static ClaimsPrincipal CreateUnauthenticatedClaimsPrincipal() =>
        new(new ClaimsIdentity()); // Not authenticated

    private void SetupSuccessfulTokenFlow(string code, string token, ClaimsPrincipal claimsPrincipal)
    {
        tokenService.GetTokenAsync(code).Returns(token);
        authenticationService.ValidateTokenAsync(token).Returns(claimsPrincipal);
    }

    private static async Task VerifySuccessfulTokenFlow(ITokenService tokenService, IAuthenticationService authenticationService,
        string code, string token, ClaimsPrincipal claimsPrincipal)
    {
        await tokenService.Received(1).GetTokenAsync(code);
        await authenticationService.Received(1).ValidateTokenAsync(token);
        await authenticationService.Received(1).SignInUserAsync(Arg.Any<HttpContext>(), claimsPrincipal);
    }

    [Fact]
    public async Task Login_WithMissingCode_ShouldReturnBadRequest()
    {
        // Act
        var result = await controller.Login(string.Empty, TestEmptyRedirectUrl);

        // Assert
        result.ShouldBeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.CodeRequired);
    }

    [Fact]
    public async Task Login_WithMissingRedirectUrl_ShouldUseDefaultRedirectUrl()
    {
        // Arrange
        var testCode = GenerateTestCode();
        var testToken = GenerateTestToken();
        var claimsPrincipal = CreateAuthenticatedClaimsPrincipal();

        SetupSuccessfulTokenFlow(testCode, testToken, claimsPrincipal);

        // Act
        var result = await controller.Login(testCode, string.Empty);

        // Assert
        result.ShouldBeOfType<RedirectResult>();
        var redirectResult = (RedirectResult)result;
        redirectResult.Url.ShouldBe(jwtOptions.DefaultRedirectUrl);

        await VerifySuccessfulTokenFlow(tokenService, authenticationService, testCode, testToken, claimsPrincipal);
    }

    [Fact]
    public async Task Login_WithValidParameters_ShouldReturnRedirect()
    {
        // Arrange
        var testCode = GenerateTestCode();
        var testToken = GenerateTestToken();
        var claimsPrincipal = CreateAuthenticatedClaimsPrincipal();

        SetupSuccessfulTokenFlow(testCode, testToken, claimsPrincipal);

        // Act
        var result = await controller.Login(testCode, TestRedirectUrl);

        // Assert
        result.ShouldBeOfType<RedirectResult>();
        var redirectResult = (RedirectResult)result;
        redirectResult.Url.ShouldBe(TestRedirectUrl);

        await VerifySuccessfulTokenFlow(tokenService, authenticationService, testCode, testToken, claimsPrincipal);
    }

    [Fact]
    public async Task Login_WithTokenValidationFailure_ShouldReturnUnauthorized()
    {
        // Arrange
        var testCode = GenerateTestCode();
        var testToken = GenerateTestToken();

        tokenService.GetTokenAsync(testCode).Returns(testToken);
        authenticationService.ValidateTokenAsync(testToken).Returns((ClaimsPrincipal)null!);

        // Act
        var result = await controller.Login(testCode, TestRedirectUrl);

        // Assert
        result.ShouldBeOfType<UnauthorizedObjectResult>();
        var unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.TokenValidationFailed);
    }

    [Fact]
    public async Task Login_WithEmptyTokenResponse_ShouldReturnUnauthorized()
    {
        // Arrange
        var testCode = GenerateTestCode();

        tokenService.GetTokenAsync(testCode).Returns(string.Empty);

        // Act
        var result = await controller.Login(testCode, TestRedirectUrl);

        // Assert
        result.ShouldBeOfType<UnauthorizedObjectResult>();
        var unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.TokenRetrievalFailed);
    }

    [Fact]
    public async Task Login_WithTokenServiceException_ShouldReturnInternalServerError()
    {
        // Arrange
        var testCode = GenerateTestCode();

        tokenService.GetTokenAsync(testCode)
            .Returns(Task.FromException<string>(new InvalidOperationException(TestExceptionMessage)));

        // Act
        var result = await controller.Login(testCode, TestRedirectUrl);

        // Assert
        result.ShouldBeOfType<ObjectResult>();
        var objectResult = (ObjectResult)result;
        objectResult.StatusCode.ShouldBe(AuthenticationConstants.HttpStatusCodes.InternalServerError);
        objectResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.AuthenticationError);
    }

    [Fact]
    public async Task Login_WithUnauthenticatedClaimsPrincipal_ShouldReturnUnauthorized()
    {
        // Arrange
        var testCode = GenerateTestCode();
        var testToken = GenerateTestToken();
        var unauthenticatedPrincipal = CreateUnauthenticatedClaimsPrincipal();

        tokenService.GetTokenAsync(testCode).Returns(testToken);
        authenticationService.ValidateTokenAsync(testToken).Returns(unauthenticatedPrincipal);

        // Act
        var result = await controller.Login(testCode, TestRedirectUrl);

        // Assert
        result.ShouldBeOfType<UnauthorizedObjectResult>();
        var unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.ShouldBe(AuthenticationConstants.ErrorMessages.TokenValidationFailed);
    }
}
