using Axon.Core.Authentication.Services;
using CommunityToolkit.Diagnostics;
using Microsoft.AspNetCore.Http;
using NSubstitute;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Tests.Controllers;

/// <summary>
/// Common helper methods for controller tests to reduce code duplication and improve maintainability.
/// </summary>
public static class TestHelpers
{
    /// <summary>
    /// Generates a random test code for authentication testing.
    /// </summary>
    /// <returns>A random string to use as a test code.</returns>
    public static string GenerateTestCode() => Fake.GetRandomString(10);

    /// <summary>
    /// Generates a random test token for authentication testing.
    /// </summary>
    /// <returns>A random string to use as a test token.</returns>
    public static string GenerateTestToken() => Fake.GetRandomString(10);

    /// <summary>
    /// Creates an authenticated ClaimsPrincipal for testing successful authentication scenarios.
    /// </summary>
    /// <returns>A ClaimsPrincipal with an authenticated identity.</returns>
    public static ClaimsPrincipal CreateAuthenticatedClaimsPrincipal() => 
        new(new ClaimsIdentity(authenticationType: TestConstants.AuthenticationType));

    /// <summary>
    /// Creates an unauthenticated ClaimsPrincipal for testing authentication failure scenarios.
    /// </summary>
    /// <returns>A ClaimsPrincipal with an unauthenticated identity.</returns>
    public static ClaimsPrincipal CreateUnauthenticatedClaimsPrincipal() => 
        new(new ClaimsIdentity()); // Not authenticated

    /// <summary>
    /// Sets up the token service and authentication service mocks for a successful authentication flow.
    /// </summary>
    /// <param name="tokenService">The token service mock to configure.</param>
    /// <param name="authenticationService">The authentication service mock to configure.</param>
    /// <param name="code">The code that should be exchanged for a token.</param>
    /// <param name="token">The token that should be returned by the token service.</param>
    /// <param name="claimsPrincipal">The claims principal that should be returned by token validation.</param>
    public static void SetupSuccessfulTokenFlow(
        ITokenService tokenService, 
        IAuthenticationService authenticationService,
        string code, 
        string token, 
        ClaimsPrincipal claimsPrincipal)
    {
        tokenService.GetTokenAsync(code).Returns(token);
        authenticationService.ValidateTokenAsync(token).Returns(claimsPrincipal);
    }

    /// <summary>
    /// Verifies that the token service and authentication service were called correctly for a successful authentication flow.
    /// </summary>
    /// <param name="tokenService">The token service mock to verify.</param>
    /// <param name="authenticationService">The authentication service mock to verify.</param>
    /// <param name="code">The code that should have been used to get the token.</param>
    /// <param name="token">The token that should have been validated.</param>
    /// <param name="claimsPrincipal">The claims principal that should have been used for sign-in.</param>
    public static async Task VerifySuccessfulTokenFlow(
        ITokenService tokenService, 
        IAuthenticationService authenticationService, 
        string code, 
        string token, 
        ClaimsPrincipal claimsPrincipal)
    {
        await tokenService.Received(1).GetTokenAsync(code);
        await authenticationService.Received(1).ValidateTokenAsync(token);
        await authenticationService.Received(1).SignInUserAsync(Arg.Any<HttpContext>(), claimsPrincipal);
    }
}
