using Axon.Core.Authentication.Services;
using CommunityToolkit.Diagnostics;
using Microsoft.AspNetCore.Http;
using NSubstitute;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Tests;

public static class TestHelpers
{
    public static string GenerateTestCode() => Fake.GetRandomString(10);

    public static string GenerateTestToken() => Fake.GetRandomString(10);

    public static ClaimsPrincipal CreateAuthenticatedClaimsPrincipal() =>
        new(new ClaimsIdentity(authenticationType: TestConstants.AuthenticationType));

    public static ClaimsPrincipal CreateUnauthenticatedClaimsPrincipal() =>
        new(new ClaimsIdentity());

    public static void SetupSuccessfulTokenFlow(
        ITokenService tokenService,
        IAuthenticationService authenticationService,
        string code,
        string token,
        ClaimsPrincipal claimsPrincipal)
    {
        tokenService.GetTokenAsync(code).Returns(token);
        authenticationService.ValidateTokenAsync(token).Returns(claimsPrincipal);
    }

    public static async Task VerifySuccessfulTokenFlow(
        ITokenService tokenService,
        IAuthenticationService authenticationService,
        string code,
        string token,
        ClaimsPrincipal claimsPrincipal)
    {
        await tokenService.Received(1).GetTokenAsync(code);
        await authenticationService.Received(1).ValidateTokenAsync(token);
        await authenticationService.Received(1).SignInUserAsync(Arg.Any<HttpContext>(), claimsPrincipal);
    }
}
