using Axon.Core.Authentication.Interfaces;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.Linq;

namespace Axon.Core.Authentication.Configuration;

public class ConfigureTokenValidationParameters(
    IOptionsMonitor<JwtAuthenticationOptions> jwtOptions,
    IJwtKeyProvider keyProvider) : IPostConfigureOptions<JwtBearerOptions>
{
    private readonly JwtAuthenticationOptions jwtAuthenticationOptions = jwtOptions.CurrentValue;

    public void PostConfigure(string? name, JwtBearerOptions options)
    {
        if (name == JwtBearerAuthenticationDefaults.AuthenticationScheme)
        {
            options.TokenValidationParameters = CreateTokenValidationParameters();
        }
    }

    private TokenValidationParameters CreateTokenValidationParameters()
    {
        // Get the first available key for signing validation
        // In a production scenario, you might want to handle multiple keys or specific key selection
        var keys = keyProvider.GetKeyAsync(string.Empty).GetAwaiter().GetResult();
        var signingKey = keys.FirstOrDefault();

        return new TokenValidationParameters
        {
            ValidIssuer = jwtAuthenticationOptions.Issuer,
            ValidAudience = jwtAuthenticationOptions.Audience,
            ClockSkew = jwtAuthenticationOptions.ClockSkew,
            IssuerSigningKey = signingKey,
            ValidateIssuerSigningKey = true,
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true
        };
    }
}
