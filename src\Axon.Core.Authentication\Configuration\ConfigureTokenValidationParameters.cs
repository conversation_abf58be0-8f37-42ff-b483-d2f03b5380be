using Axon.Core.Authentication.Interfaces;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Configuration;

public class ConfigureTokenValidationParameters(
    IOptionsMonitor<JwtAuthenticationOptions> jwtOptions,
    IJwtKeyProvider keyProvider) : IPostConfigureOptions<JwtBearerOptions>
{
    private readonly JwtAuthenticationOptions jwtAuthenticationOptions = jwtOptions.CurrentValue;

    public void PostConfigure(string? name, JwtBearerOptions options)
    {
        if (name == JwtBearerAuthenticationDefaults.AuthenticationScheme)
        {
            options.TokenValidationParameters = CreateTokenValidationParameters();
        }
    }

    private TokenValidationParameters CreateTokenValidationParameters()
    {
        return new TokenValidationParameters
        {
            ValidIssuer = jwtAuthenticationOptions.Issuer,
            ValidAudience = jwtAuthenticationOptions.Audience,
            ClockSkew = jwtAuthenticationOptions.ClockSkew,

            // Use async key resolver to fetch keys dynamically
            IssuerSigningKeyResolver = async (token, securityToken, kid, parameters) =>
            {
                var keys = await keyProvider.GetKeyAsync(kid ?? string.Empty);
                return keys;
            },

            ValidateIssuerSigningKey = true,
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true
        };
    }
}
